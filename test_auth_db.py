#!/usr/bin/env python3
"""
Test script to verify auth database connectivity and table existence
"""

import os
import sqlite3
from datetime import datetime

def test_auth_database():
    """Test the auth database connectivity"""
    print("=== Testing Auth Database Connectivity ===")
    
    # Check current working directory
    print(f"Current working directory: {os.getcwd()}")
    
    # Check if database file exists
    DB_PATH = "user_credits.db"
    print(f"Database path: {DB_PATH}")
    print(f"Database exists: {os.path.exists(DB_PATH)}")
    
    if os.path.exists(DB_PATH):
        print(f"Database size: {os.path.getsize(DB_PATH)} bytes")
    
    try:
        # Test connection
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        # Check tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        print(f"Tables found: {[table[0] for table in tables]}")
        
        # Check if user_sessions table exists and has correct structure
        if 'user_sessions' in [table[0] for table in tables]:
            print("✓ user_sessions table exists")
            
            # Check table structure
            cursor.execute("PRAGMA table_info(user_sessions)")
            columns = cursor.fetchall()
            print("user_sessions columns:")
            for col in columns:
                print(f"  {col[1]} {col[2]}")
            
            # Test a simple query
            cursor.execute("SELECT COUNT(*) FROM user_sessions")
            count = cursor.fetchone()[0]
            print(f"✓ user_sessions has {count} records")
            
            # Test cleanup query (the one that's failing)
            try:
                cursor.execute("DELETE FROM user_sessions WHERE expires_at < ?", (datetime.now(),))
                print("✓ Cleanup query works")
                conn.rollback()  # Don't actually delete anything
            except Exception as e:
                print(f"✗ Cleanup query failed: {e}")
                
        else:
            print("✗ user_sessions table does not exist")
            
        conn.close()
        print("✓ Database connection test successful")
        return True
        
    except Exception as e:
        print(f"✗ Database connection failed: {e}")
        return False

def test_auth_functions():
    """Test importing and calling auth functions"""
    print("\n=== Testing Auth Functions ===")
    
    try:
        # Test importing auth module
        from auth import cleanup_expired_sessions, init_database
        print("✓ Auth module imported successfully")
        
        # Test init_database function
        try:
            init_database()
            print("✓ init_database() executed successfully")
        except Exception as e:
            print(f"✗ init_database() failed: {e}")
        
        # Test cleanup_expired_sessions function
        try:
            cleanup_expired_sessions()
            print("✓ cleanup_expired_sessions() executed successfully")
        except Exception as e:
            print(f"✗ cleanup_expired_sessions() failed: {e}")
            
        return True
        
    except Exception as e:
        print(f"✗ Auth function test failed: {e}")
        return False

if __name__ == "__main__":
    print("Testing auth database connectivity...")
    
    db_test = test_auth_database()
    func_test = test_auth_functions()
    
    print(f"\n=== Summary ===")
    print(f"Database test: {'✓ PASSED' if db_test else '✗ FAILED'}")
    print(f"Function test: {'✓ PASSED' if func_test else '✗ FAILED'}")
    
    if db_test and func_test:
        print("✓ All tests passed - auth database should work correctly")
    else:
        print("✗ Some tests failed - this explains the database issues")
